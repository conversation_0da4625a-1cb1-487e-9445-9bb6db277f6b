<?php
/**
 * ExtremeLife MLM Admin Mass Password Reset Interface
 * Web-based tool for updating all member passwords to "test123"
 */

session_start();

require_once 'config/database.php';
require_once 'auth_config.php';
require_once 'enhanced_auth.php';

// Security check - only allow in testing mode
if (!AuthConfig::isTestingMode()) {
    die('This tool is only available in testing mode. Please enable testing mode in auth_config.php');
}

$message = '';
$error = '';
$log_entries = [];
$execution_results = null;

// Initialize database connection
$config = include 'config/database.php';
$pdo = new PDO("mysql:host={$config['host']};dbname={$config['dbname']}", $config['username'], $config['password']);
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    if ($action === 'execute_reset') {
        $confirmation = $_POST['confirmation'] ?? '';
        
        if ($confirmation !== 'RESET ALL PASSWORDS') {
            $error = 'Please type the exact confirmation phrase to proceed';
        } else {
            $execution_results = executePasswordReset($pdo);
        }
    } elseif ($action === 'preview') {
        $execution_results = previewPasswordReset($pdo);
    }
}

function executePasswordReset($pdo) {
    $results = [
        'success' => false,
        'message' => '',
        'details' => [],
        'backup_table' => '',
        'updated_count' => 0
    ];
    
    try {
        $backup_table = 'mlm_password_backup_' . date('Y_m_d_H_i_s');
        $new_password = 'test123';
        $new_password_hash = password_hash($new_password, PASSWORD_DEFAULT);
        
        // Create backup
        $sql = "CREATE TABLE {$backup_table} AS 
                SELECT member_id, password_hash, failed_attempts, locked_until, 
                       last_login, created_at, NOW() as backup_created_at
                FROM mlm_user_credentials";
        $pdo->exec($sql);
        
        $results['details'][] = "✅ Created backup table: {$backup_table}";
        $results['backup_table'] = $backup_table;
        
        // Get member count
        $stmt = $pdo->query("SELECT COUNT(*) FROM mlm_members");
        $total_members = $stmt->fetchColumn();
        
        // Update all passwords
        $pdo->beginTransaction();
        
        // Update existing credentials
        $stmt = $pdo->prepare("
            UPDATE mlm_user_credentials 
            SET password_hash = ?, 
                password_reset_at = NOW(),
                failed_attempts = 0,
                locked_until = NULL
        ");
        $stmt->execute([$new_password_hash]);
        $updated_existing = $stmt->rowCount();
        
        // Create credentials for members without them
        $stmt = $pdo->prepare("
            INSERT INTO mlm_user_credentials (member_id, password_hash, created_at, password_reset_at)
            SELECT m.id, ?, NOW(), NOW()
            FROM mlm_members m
            LEFT JOIN mlm_user_credentials uc ON m.id = uc.member_id
            WHERE uc.member_id IS NULL
        ");
        $stmt->execute([$new_password_hash]);
        $created_new = $stmt->rowCount();
        
        $pdo->commit();
        
        $results['updated_count'] = $updated_existing + $created_new;
        $results['details'][] = "✅ Updated {$updated_existing} existing password records";
        $results['details'][] = "✅ Created {$created_new} new password records";
        $results['details'][] = "✅ Total members processed: {$total_members}";
        
        // Log audit event
        $stmt = $pdo->prepare("
            INSERT INTO mlm_auth_audit 
            (member_id, username, action, ip_address, user_agent, success, failure_reason) 
            VALUES (NULL, 'ADMIN_WEB', 'mass_password_reset', ?, ?, TRUE, ?)
        ");
        $stmt->execute([
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            "Updated {$results['updated_count']} passwords to test123 via web interface"
        ]);
        
        $results['details'][] = "✅ Audit event logged";
        
        // Verify a few passwords
        $stmt = $pdo->prepare("
            SELECT uc.member_id, m.first_name, m.last_name, uc.password_hash
            FROM mlm_user_credentials uc
            JOIN mlm_members m ON uc.member_id = m.id
            LIMIT 3
        ");
        $stmt->execute();
        $sample_members = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $verified_count = 0;
        foreach ($sample_members as $member) {
            if (password_verify($new_password, $member['password_hash'])) {
                $verified_count++;
                $results['details'][] = "✅ Verified password for {$member['first_name']} {$member['last_name']} (ID: {$member['member_id']})";
            }
        }
        
        $results['success'] = true;
        $results['message'] = "Successfully updated all member passwords to 'test123'";
        
    } catch (Exception $e) {
        if (isset($pdo) && $pdo->inTransaction()) {
            $pdo->rollback();
        }
        $results['message'] = "Error: " . $e->getMessage();
        $results['details'][] = "❌ " . $e->getMessage();
    }
    
    return $results;
}

function previewPasswordReset($pdo) {
    $results = [
        'success' => true,
        'message' => 'Preview of password reset operation',
        'details' => [],
        'preview' => true
    ];
    
    try {
        // Get member statistics
        $stmt = $pdo->query("
            SELECT 
                COUNT(*) as total_members,
                COUNT(uc.member_id) as members_with_credentials,
                COUNT(*) - COUNT(uc.member_id) as members_without_credentials
            FROM mlm_members m
            LEFT JOIN mlm_user_credentials uc ON m.id = uc.member_id
        ");
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $results['details'][] = "📊 Total members in system: {$stats['total_members']}";
        $results['details'][] = "📊 Members with existing credentials: {$stats['members_with_credentials']}";
        $results['details'][] = "📊 Members without credentials: {$stats['members_without_credentials']}";
        
        // Get sample members
        $stmt = $pdo->query("
            SELECT m.id, m.first_name, m.last_name, m.email, m.current_rank,
                   CASE WHEN uc.member_id IS NOT NULL THEN 'Yes' ELSE 'No' END as has_credentials
            FROM mlm_members m
            LEFT JOIN mlm_user_credentials uc ON m.id = uc.member_id
            ORDER BY m.id
            LIMIT 10
        ");
        $sample_members = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $results['details'][] = "👥 Sample members (first 10):";
        foreach ($sample_members as $member) {
            $results['details'][] = "   • ID {$member['id']}: {$member['first_name']} {$member['last_name']} ({$member['current_rank']}) - Credentials: {$member['has_credentials']}";
        }
        
        $results['details'][] = "";
        $results['details'][] = "🔄 Planned actions:";
        $results['details'][] = "   1. Create backup table with timestamp";
        $results['details'][] = "   2. Update {$stats['members_with_credentials']} existing password hashes";
        $results['details'][] = "   3. Create {$stats['members_without_credentials']} new credential records";
        $results['details'][] = "   4. Reset all failed login attempts and account locks";
        $results['details'][] = "   5. Set all passwords to 'test123'";
        $results['details'][] = "   6. Log audit event";
        $results['details'][] = "   7. Verify password updates";
        
    } catch (Exception $e) {
        $results['success'] = false;
        $results['message'] = "Preview error: " . $e->getMessage();
    }
    
    return $results;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mass Password Reset - ExtremeLife MLM Admin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .warning-banner {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
            color: #856404;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            font-weight: 600;
            text-align: center;
        }
        
        .testing-banner {
            background: #e7f3ff;
            border: 2px solid #b3d9ff;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 600;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .card-title {
            color: #2d5a27;
            font-size: 1.5rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s;
            min-height: 44px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-1px);
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            min-height: 44px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #2d5a27;
        }
        
        .confirmation-input {
            border-color: #dc3545 !important;
            background: #fff5f5;
        }
        
        .message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .results-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .log-entry {
            font-family: monospace;
            font-size: 12px;
            margin-bottom: 5px;
            padding: 5px;
            background: white;
            border-radius: 4px;
        }
        
        .rollback-instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .rollback-instructions h4 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .rollback-instructions code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            display: block;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚠️ ExtremeLife MLM Mass Password Reset</h1>
            <p>Administrative Tool for Testing Environment</p>
        </div>
        
        <div class="testing-banner">
            🧪 TESTING MODE ACTIVE - This tool is only available in development mode
        </div>
        
        <div class="warning-banner">
            ⚠️ DANGER: This will change ALL member passwords to "test123"<br>
            This action affects the entire member database and should only be used for testing!
        </div>
        
        <?php if ($message): ?>
            <div class="message">✅ <?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="error">❌ <?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <!-- Preview Section -->
        <div class="card">
            <h2 class="card-title">📊 Preview Impact</h2>
            <p style="margin-bottom: 20px;">Review what will be affected before proceeding with the password reset.</p>
            
            <form method="POST">
                <input type="hidden" name="action" value="preview">
                <button type="submit" class="btn btn-warning">Preview Changes</button>
            </form>
        </div>
        
        <!-- Execution Section -->
        <div class="card">
            <h2 class="card-title">🔄 Execute Password Reset</h2>
            
            <form method="POST" onsubmit="return confirmExecution()">
                <input type="hidden" name="action" value="execute_reset">
                
                <div class="form-group">
                    <label for="confirmation">Type "RESET ALL PASSWORDS" to confirm:</label>
                    <input type="text" id="confirmation" name="confirmation" class="form-control confirmation-input" 
                           placeholder="Type the exact phrase to confirm" required>
                </div>
                
                <button type="submit" class="btn btn-danger">🚨 Execute Mass Password Reset</button>
            </form>
        </div>
        
        <!-- Results Display -->
        <?php if ($execution_results): ?>
            <div class="card">
                <h2 class="card-title">
                    <?php echo isset($execution_results['preview']) ? '👁️ Preview Results' : '📋 Execution Results'; ?>
                </h2>
                
                <div class="results-container">
                    <?php if ($execution_results['success']): ?>
                        <div class="message"><?php echo htmlspecialchars($execution_results['message']); ?></div>
                    <?php else: ?>
                        <div class="error"><?php echo htmlspecialchars($execution_results['message']); ?></div>
                    <?php endif; ?>
                    
                    <?php if (!empty($execution_results['details'])): ?>
                        <h4>Details:</h4>
                        <?php foreach ($execution_results['details'] as $detail): ?>
                            <div class="log-entry"><?php echo htmlspecialchars($detail); ?></div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                
                <?php if ($execution_results['success'] && !isset($execution_results['preview']) && !empty($execution_results['backup_table'])): ?>
                    <div class="rollback-instructions">
                        <h4>🔄 Rollback Instructions</h4>
                        <p>If you need to restore the original passwords, execute this SQL:</p>
                        <code>UPDATE mlm_user_credentials uc
JOIN <?php echo htmlspecialchars($execution_results['backup_table']); ?> backup ON uc.member_id = backup.member_id
SET uc.password_hash = backup.password_hash,
    uc.failed_attempts = backup.failed_attempts,
    uc.locked_until = backup.locked_until;

-- Then drop the backup table:
DROP TABLE <?php echo htmlspecialchars($execution_results['backup_table']); ?>;</code>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="member_login.php" class="btn btn-secondary">← Back to Login</a>
            <a href="test_user_generator.php" class="btn btn-secondary">Test User Generator</a>
        </div>
    </div>
    
    <script>
        function confirmExecution() {
            const confirmation = document.getElementById('confirmation').value;
            if (confirmation !== 'RESET ALL PASSWORDS') {
                alert('Please type the exact confirmation phrase: RESET ALL PASSWORDS');
                return false;
            }
            
            return confirm('Are you absolutely sure you want to reset ALL member passwords to "test123"?\n\nThis action cannot be undone without using the rollback SQL commands.');
        }
    </script>
</body>
</html>
