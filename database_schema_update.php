<?php
/**
 * ExtremeLife MLM Database Schema Update
 * Adds missing columns for enhanced authentication system
 */

require_once 'config/database.php';

// Initialize database connection
$config = include 'config/database.php';
$pdo = new PDO("mysql:host={$config['host']};dbname={$config['dbname']}", $config['username'], $config['password']);
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

$updates_applied = [];
$errors = [];

try {
    echo "🔧 ExtremeLife MLM Database Schema Update\n";
    echo "========================================\n\n";
    
    // Check current table structure
    echo "📋 Checking current mlm_user_credentials table structure...\n";
    $stmt = $pdo->query("DESCRIBE mlm_user_credentials");
    $existing_columns = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $existing_columns[] = $row['Field'];
    }
    
    echo "Current columns: " . implode(', ', $existing_columns) . "\n\n";
    
    // Define required columns for enhanced authentication
    $required_columns = [
        'failed_attempts' => 'INT DEFAULT 0',
        'locked_until' => 'TIMESTAMP NULL',
        'last_login' => 'TIMESTAMP NULL',
        'two_factor_enabled' => 'BOOLEAN DEFAULT FALSE',
        'two_factor_secret' => 'VARCHAR(255) NULL',
        'password_reset_at' => 'TIMESTAMP NULL'
    ];
    
    // Add missing columns
    echo "🔄 Adding missing columns to mlm_user_credentials table...\n";
    
    foreach ($required_columns as $column => $definition) {
        if (!in_array($column, $existing_columns)) {
            try {
                $sql = "ALTER TABLE mlm_user_credentials ADD COLUMN {$column} {$definition}";
                $pdo->exec($sql);
                $updates_applied[] = "✅ Added column: {$column}";
                echo "✅ Added column: {$column}\n";
            } catch (Exception $e) {
                $error_msg = "❌ Failed to add column {$column}: " . $e->getMessage();
                $errors[] = $error_msg;
                echo $error_msg . "\n";
            }
        } else {
            echo "ℹ️  Column {$column} already exists\n";
        }
    }
    
    // Create authentication audit table if it doesn't exist
    echo "\n🔄 Creating authentication audit table...\n";
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS mlm_auth_audit (
                id INT AUTO_INCREMENT PRIMARY KEY,
                member_id INT NULL,
                username VARCHAR(100),
                action VARCHAR(50),
                ip_address VARCHAR(45),
                user_agent TEXT,
                success BOOLEAN,
                failure_reason VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_member_id (member_id),
                INDEX idx_created_at (created_at)
            )
        ");
        $updates_applied[] = "✅ Created/verified mlm_auth_audit table";
        echo "✅ Created/verified mlm_auth_audit table\n";
    } catch (Exception $e) {
        $error_msg = "❌ Failed to create mlm_auth_audit table: " . $e->getMessage();
        $errors[] = $error_msg;
        echo $error_msg . "\n";
    }
    
    // Create remember me tokens table if it doesn't exist
    echo "\n🔄 Creating remember me tokens table...\n";
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS mlm_remember_tokens (
                id INT AUTO_INCREMENT PRIMARY KEY,
                member_id INT NOT NULL,
                token_hash VARCHAR(255) NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_member_id (member_id),
                INDEX idx_token_hash (token_hash),
                INDEX idx_expires_at (expires_at)
            )
        ");
        $updates_applied[] = "✅ Created/verified mlm_remember_tokens table";
        echo "✅ Created/verified mlm_remember_tokens table\n";
    } catch (Exception $e) {
        $error_msg = "❌ Failed to create mlm_remember_tokens table: " . $e->getMessage();
        $errors[] = $error_msg;
        echo $error_msg . "\n";
    }
    
    // Create password reset tokens table if it doesn't exist
    echo "\n🔄 Creating password reset tokens table...\n";
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS mlm_password_reset_tokens (
                id INT AUTO_INCREMENT PRIMARY KEY,
                member_id INT NOT NULL,
                token VARCHAR(255) NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                used BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_member_id (member_id),
                INDEX idx_token (token)
            )
        ");
        $updates_applied[] = "✅ Created/verified mlm_password_reset_tokens table";
        echo "✅ Created/verified mlm_password_reset_tokens table\n";
    } catch (Exception $e) {
        $error_msg = "❌ Failed to create mlm_password_reset_tokens table: " . $e->getMessage();
        $errors[] = $error_msg;
        echo $error_msg . "\n";
    }
    
    // Add is_test_account column to mlm_members if it doesn't exist
    echo "\n🔄 Checking mlm_members table for is_test_account column...\n";
    $stmt = $pdo->query("DESCRIBE mlm_members");
    $member_columns = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $member_columns[] = $row['Field'];
    }
    
    if (!in_array('is_test_account', $member_columns)) {
        try {
            $pdo->exec("ALTER TABLE mlm_members ADD COLUMN is_test_account BOOLEAN DEFAULT FALSE");
            $updates_applied[] = "✅ Added is_test_account column to mlm_members";
            echo "✅ Added is_test_account column to mlm_members\n";
        } catch (Exception $e) {
            $error_msg = "❌ Failed to add is_test_account column: " . $e->getMessage();
            $errors[] = $error_msg;
            echo $error_msg . "\n";
        }
    } else {
        echo "ℹ️  Column is_test_account already exists in mlm_members\n";
    }
    
    // Verify final table structure
    echo "\n📋 Final mlm_user_credentials table structure:\n";
    $stmt = $pdo->query("DESCRIBE mlm_user_credentials");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "  • {$row['Field']} ({$row['Type']}) - {$row['Null']} - {$row['Default']}\n";
    }
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "📊 SCHEMA UPDATE SUMMARY\n";
    echo str_repeat("=", 50) . "\n";
    
    if (!empty($updates_applied)) {
        echo "✅ SUCCESSFUL UPDATES:\n";
        foreach ($updates_applied as $update) {
            echo "  {$update}\n";
        }
    }
    
    if (!empty($errors)) {
        echo "\n❌ ERRORS ENCOUNTERED:\n";
        foreach ($errors as $error) {
            echo "  {$error}\n";
        }
    }
    
    if (empty($errors)) {
        echo "\n🎉 Database schema update completed successfully!\n";
        echo "The enhanced authentication system is now ready to use.\n";
    } else {
        echo "\n⚠️  Schema update completed with some errors.\n";
        echo "Please review the errors above and fix them manually if needed.\n";
    }
    
} catch (Exception $e) {
    echo "❌ CRITICAL ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
