<?php
/**
 * ExtremeLife MLM Password Reset Request
 */

session_start();

require_once 'config/database.php';
require_once 'enhanced_auth.php';

$message = '';
$error = '';
$reset_token = '';

// Initialize database connection
$config = include 'config/database.php';
$pdo = new PDO("mysql:host={$config['host']};dbname={$config['dbname']}", $config['username'], $config['password']);
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

$auth = new EnhancedAuth($pdo);

// Handle password reset request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    
    if (empty($email)) {
        $error = 'Please enter your email address';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address';
    } else {
        $result = $auth->initiatePasswordReset($email);
        
        if ($result['success']) {
            $message = $result['message'];
            if (isset($result['reset_token'])) {
                $reset_token = $result['reset_token'];
            }
        } else {
            $error = $result['message'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - ExtremeLife MLM</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2d5a27, #4a7c59, #6b8e6b);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .reset-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
            padding: 60px 40px;
            text-align: center;
        }
        
        .reset-icon {
            font-size: 4rem;
            color: #2d5a27;
            margin-bottom: 20px;
        }
        
        .reset-title {
            color: #2d5a27;
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .reset-subtitle {
            color: #666;
            margin-bottom: 40px;
            line-height: 1.6;
        }
        
        .testing-mode-banner {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .form-group {
            margin-bottom: 25px;
            text-align: left;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
            min-height: 44px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #2d5a27;
            box-shadow: 0 0 0 3px rgba(45, 90, 39, 0.1);
        }
        
        .btn-reset {
            background: linear-gradient(135deg, #2d5a27, #4a7c59);
            color: white;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
            min-height: 44px;
            margin-bottom: 25px;
        }
        
        .btn-reset:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(45, 90, 39, 0.3);
        }
        
        .back-link {
            color: #2d5a27;
            text-decoration: none;
            font-weight: 600;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .reset-token-display {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-family: monospace;
            word-break: break-all;
        }
        
        .reset-token-display h4 {
            color: #2d5a27;
            margin-bottom: 10px;
            font-family: inherit;
        }
        
        @media (max-width: 768px) {
            .reset-container {
                padding: 40px 20px;
            }
            
            .reset-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-icon">🔐</div>
        <h1 class="reset-title">Forgot Password?</h1>
        <p class="reset-subtitle">
            No worries! Enter your email address and we'll send you instructions to reset your password.
        </p>
        
        <?php if (AuthConfig::isTestingMode()): ?>
            <div class="testing-mode-banner">
                🧪 TESTING MODE: Password reset tokens will be displayed for testing
            </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="error-message">❌ <?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <?php if ($message): ?>
            <div class="success-message">✅ <?php echo htmlspecialchars($message); ?></div>
            
            <?php if ($reset_token): ?>
                <div class="reset-token-display">
                    <h4>Testing Reset Token:</h4>
                    <p>Use this token to reset your password:</p>
                    <strong><?php echo htmlspecialchars($reset_token); ?></strong>
                    <p style="margin-top: 10px;">
                        <a href="reset_password.php?token=<?php echo urlencode($reset_token); ?>" 
                           style="color: #2d5a27; font-weight: 600;">
                            Click here to reset password
                        </a>
                    </p>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <form method="POST">
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" class="form-control" 
                           placeholder="Enter your email address" 
                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                           autocomplete="email" required>
                </div>
                
                <button type="submit" class="btn-reset">Send Reset Instructions</button>
            </form>
        <?php endif; ?>
        
        <a href="member_login.php" class="back-link">← Back to Login</a>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const emailField = document.getElementById('email');
            if (emailField) {
                emailField.focus();
            }
        });
    </script>
</body>
</html>
