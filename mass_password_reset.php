<?php
/**
 * ExtremeLife MLM Mass Password Reset Tool
 * Updates all member passwords to "test123" for testing purposes
 * 
 * SECURITY: Only works in testing mode with proper authentication
 */

// Prevent direct web access
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line for security reasons.');
}

require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/auth_config.php';
require_once __DIR__ . '/enhanced_auth.php';

class MassPasswordReset {
    private $pdo;
    private $auth;
    private $backup_table = 'mlm_password_backup_' . date('Y_m_d_H_i_s');
    private $new_password = 'test123';
    private $log_file = 'mass_password_reset.log';
    
    public function __construct() {
        // Initialize database connection
        $config = include __DIR__ . '/config/database.php';
        $this->pdo = new PDO("mysql:host={$config['host']};dbname={$config['dbname']}", 
                            $config['username'], $config['password']);
        $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $this->auth = new EnhancedAuth($this->pdo);
        
        $this->log("=== ExtremeLife MLM Mass Password Reset Started ===");
    }
    
    public function execute() {
        try {
            // Step 1: Security checks
            if (!$this->performSecurityChecks()) {
                return false;
            }
            
            // Step 2: Create backup
            if (!$this->createPasswordBackup()) {
                return false;
            }
            
            // Step 3: Get all members
            $members = $this->getAllMembers();
            if (empty($members)) {
                $this->log("ERROR: No members found in database");
                return false;
            }
            
            // Step 4: Update passwords
            $updated_count = $this->updateAllPasswords($members);
            
            // Step 5: Reset account locks and failed attempts
            $this->resetAccountLocks();
            
            // Step 6: Log audit event
            $this->logAuditEvent($updated_count);
            
            // Step 7: Verification
            $this->verifyPasswordUpdates($members);
            
            $this->log("=== Mass Password Reset Completed Successfully ===");
            $this->log("Updated {$updated_count} member passwords to '{$this->new_password}'");
            $this->log("Backup table created: {$this->backup_table}");
            
            return true;
            
        } catch (Exception $e) {
            $this->log("CRITICAL ERROR: " . $e->getMessage());
            $this->log("Stack trace: " . $e->getTraceAsString());
            return false;
        }
    }
    
    private function performSecurityChecks() {
        $this->log("Performing security checks...");
        
        // Check if testing mode is enabled
        if (!AuthConfig::isTestingMode()) {
            $this->log("ERROR: Testing mode is not enabled. Cannot proceed with mass password reset.");
            $this->log("Please set AuthConfig::TESTING_MODE = true in auth_config.php");
            return false;
        }
        
        $this->log("✅ Testing mode is enabled");
        
        // Check database connection
        try {
            $this->pdo->query("SELECT 1");
            $this->log("✅ Database connection successful");
        } catch (Exception $e) {
            $this->log("ERROR: Database connection failed - " . $e->getMessage());
            return false;
        }
        
        // Check if required tables exist
        $required_tables = ['mlm_members', 'mlm_user_credentials'];
        foreach ($required_tables as $table) {
            $stmt = $this->pdo->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$table]);
            if (!$stmt->fetch()) {
                $this->log("ERROR: Required table '{$table}' does not exist");
                return false;
            }
        }
        
        $this->log("✅ All required tables exist");
        
        // Validate new password against testing requirements
        $validation = AuthConfig::validatePassword($this->new_password);
        if (!$validation['valid']) {
            $this->log("ERROR: New password does not meet requirements: " . implode(', ', $validation['errors']));
            return false;
        }
        
        $this->log("✅ New password meets testing requirements");
        $this->log("✅ All security checks passed");
        
        return true;
    }
    
    private function createPasswordBackup() {
        $this->log("Creating password backup...");
        
        try {
            // Create backup table
            $sql = "CREATE TABLE {$this->backup_table} AS 
                    SELECT member_id, password_hash, failed_attempts, locked_until, 
                           last_login, created_at, NOW() as backup_created_at
                    FROM mlm_user_credentials";
            
            $this->pdo->exec($sql);
            
            // Verify backup
            $stmt = $this->pdo->query("SELECT COUNT(*) FROM {$this->backup_table}");
            $backup_count = $stmt->fetchColumn();
            
            $this->log("✅ Password backup created: {$this->backup_table}");
            $this->log("✅ Backed up {$backup_count} password records");
            
            return true;
            
        } catch (Exception $e) {
            $this->log("ERROR: Failed to create password backup - " . $e->getMessage());
            return false;
        }
    }
    
    private function getAllMembers() {
        $this->log("Retrieving all members...");
        
        try {
            $stmt = $this->pdo->query("
                SELECT m.id, m.first_name, m.last_name, m.email, m.current_rank,
                       uc.member_id as has_credentials
                FROM mlm_members m
                LEFT JOIN mlm_user_credentials uc ON m.id = uc.member_id
                ORDER BY m.id
            ");
            
            $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $this->log("✅ Retrieved " . count($members) . " members");
            
            return $members;
            
        } catch (Exception $e) {
            $this->log("ERROR: Failed to retrieve members - " . $e->getMessage());
            return [];
        }
    }
    
    private function updateAllPasswords($members) {
        $this->log("Updating all member passwords...");
        
        $new_password_hash = password_hash($this->new_password, PASSWORD_DEFAULT);
        $updated_count = 0;
        $created_count = 0;
        
        try {
            $this->pdo->beginTransaction();
            
            foreach ($members as $member) {
                if ($member['has_credentials']) {
                    // Update existing credentials
                    $stmt = $this->pdo->prepare("
                        UPDATE mlm_user_credentials 
                        SET password_hash = ?, 
                            password_reset_at = NOW(),
                            failed_attempts = 0,
                            locked_until = NULL
                        WHERE member_id = ?
                    ");
                    $stmt->execute([$new_password_hash, $member['id']]);
                    $updated_count++;
                } else {
                    // Create new credentials for members without them
                    $stmt = $this->pdo->prepare("
                        INSERT INTO mlm_user_credentials 
                        (member_id, password_hash, created_at, password_reset_at) 
                        VALUES (?, ?, NOW(), NOW())
                    ");
                    $stmt->execute([$member['id'], $new_password_hash]);
                    $created_count++;
                }
                
                $this->log("Updated password for member ID {$member['id']} ({$member['first_name']} {$member['last_name']})");
            }
            
            $this->pdo->commit();
            
            $this->log("✅ Password update completed");
            $this->log("✅ Updated: {$updated_count} existing credentials");
            $this->log("✅ Created: {$created_count} new credentials");
            
            return $updated_count + $created_count;
            
        } catch (Exception $e) {
            $this->pdo->rollback();
            $this->log("ERROR: Password update failed - " . $e->getMessage());
            throw $e;
        }
    }
    
    private function resetAccountLocks() {
        $this->log("Resetting account locks and failed attempts...");
        
        try {
            $stmt = $this->pdo->prepare("
                UPDATE mlm_user_credentials 
                SET failed_attempts = 0, locked_until = NULL 
                WHERE failed_attempts > 0 OR locked_until IS NOT NULL
            ");
            $stmt->execute();
            
            $affected = $stmt->rowCount();
            $this->log("✅ Reset {$affected} locked/failed accounts");
            
        } catch (Exception $e) {
            $this->log("ERROR: Failed to reset account locks - " . $e->getMessage());
        }
    }
    
    private function logAuditEvent($updated_count) {
        $this->log("Logging audit event...");
        
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO mlm_auth_audit 
                (member_id, username, action, ip_address, user_agent, success, failure_reason, created_at) 
                VALUES (NULL, 'SYSTEM', 'mass_password_reset', 'CLI', 'Mass Password Reset Script', TRUE, 
                        'Updated {$updated_count} passwords to test123 for testing', NOW())
            ");
            $stmt->execute();
            
            $this->log("✅ Audit event logged");
            
        } catch (Exception $e) {
            $this->log("WARNING: Failed to log audit event - " . $e->getMessage());
        }
    }
    
    private function verifyPasswordUpdates($members) {
        $this->log("Verifying password updates...");
        
        $verification_count = 0;
        $sample_size = min(5, count($members)); // Test first 5 members
        
        for ($i = 0; $i < $sample_size; $i++) {
            $member = $members[$i];
            
            try {
                $stmt = $this->pdo->prepare("
                    SELECT password_hash FROM mlm_user_credentials WHERE member_id = ?
                ");
                $stmt->execute([$member['id']]);
                $stored_hash = $stmt->fetchColumn();
                
                if ($stored_hash && password_verify($this->new_password, $stored_hash)) {
                    $verification_count++;
                    $this->log("✅ Verified password for member ID {$member['id']}");
                } else {
                    $this->log("❌ Password verification failed for member ID {$member['id']}");
                }
                
            } catch (Exception $e) {
                $this->log("ERROR: Verification failed for member ID {$member['id']} - " . $e->getMessage());
            }
        }
        
        $this->log("✅ Password verification completed: {$verification_count}/{$sample_size} successful");
    }
    
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[{$timestamp}] {$message}" . PHP_EOL;
        
        // Output to console
        echo $log_entry;
        
        // Write to log file
        file_put_contents($this->log_file, $log_entry, FILE_APPEND | LOCK_EX);
    }
    
    public function showRollbackInstructions() {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "ROLLBACK INSTRUCTIONS (if needed):\n";
        echo str_repeat("=", 60) . "\n";
        echo "To restore original passwords, run this SQL:\n\n";
        echo "UPDATE mlm_user_credentials uc\n";
        echo "JOIN {$this->backup_table} backup ON uc.member_id = backup.member_id\n";
        echo "SET uc.password_hash = backup.password_hash,\n";
        echo "    uc.failed_attempts = backup.failed_attempts,\n";
        echo "    uc.locked_until = backup.locked_until;\n\n";
        echo "Then drop the backup table:\n";
        echo "DROP TABLE {$this->backup_table};\n";
        echo str_repeat("=", 60) . "\n";
    }
}

// Main execution
if (php_sapi_name() === 'cli') {
    echo "ExtremeLife MLM Mass Password Reset Tool\n";
    echo "========================================\n\n";
    
    // Confirm execution
    echo "This will update ALL member passwords to 'test123'.\n";
    echo "Are you sure you want to continue? (yes/no): ";
    $handle = fopen("php://stdin", "r");
    $confirmation = trim(fgets($handle));
    fclose($handle);
    
    if (strtolower($confirmation) !== 'yes') {
        echo "Operation cancelled.\n";
        exit(1);
    }
    
    $reset_tool = new MassPasswordReset();
    $success = $reset_tool->execute();
    
    if ($success) {
        echo "\n✅ Mass password reset completed successfully!\n";
        echo "All member passwords are now set to: test123\n\n";
        $reset_tool->showRollbackInstructions();
        exit(0);
    } else {
        echo "\n❌ Mass password reset failed. Check the log file for details.\n";
        exit(1);
    }
} else {
    die('This script must be run from command line.');
}
?>
