<?php
/**
 * ExtremeLife MLM Password Reset Completion
 */

session_start();

require_once 'config/database.php';
require_once 'enhanced_auth.php';

$message = '';
$error = '';
$token = $_GET['token'] ?? '';

if (empty($token)) {
    header('Location: forgot_password.php');
    exit;
}

// Initialize database connection
$config = include 'config/database.php';
$pdo = new PDO("mysql:host={$config['host']};dbname={$config['dbname']}", $config['username'], $config['password']);
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

$auth = new EnhancedAuth($pdo);

// Handle password reset
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    if (empty($new_password) || empty($confirm_password)) {
        $error = 'Please fill in all fields';
    } elseif ($new_password !== $confirm_password) {
        $error = 'Passwords do not match';
    } else {
        $result = $auth->resetPassword($token, $new_password);
        
        if ($result['success']) {
            $message = $result['message'];
        } else {
            $error = $result['message'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - ExtremeLife MLM</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2d5a27, #4a7c59, #6b8e6b);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .reset-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
            padding: 60px 40px;
            text-align: center;
        }
        
        .reset-icon {
            font-size: 4rem;
            color: #2d5a27;
            margin-bottom: 20px;
        }
        
        .reset-title {
            color: #2d5a27;
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .reset-subtitle {
            color: #666;
            margin-bottom: 40px;
            line-height: 1.6;
        }
        
        .form-group {
            margin-bottom: 25px;
            text-align: left;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
            min-height: 44px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #2d5a27;
            box-shadow: 0 0 0 3px rgba(45, 90, 39, 0.1);
        }
        
        .password-container {
            position: relative;
        }
        
        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            font-size: 18px;
            min-height: 44px;
            width: 44px;
        }
        
        .password-strength {
            margin-top: 8px;
            height: 4px;
            background: #e9ecef;
            border-radius: 2px;
            overflow: hidden;
        }
        
        .password-strength-bar {
            height: 100%;
            transition: all 0.3s;
            border-radius: 2px;
        }
        
        .strength-weak { background: #dc3545; width: 25%; }
        .strength-fair { background: #ffc107; width: 50%; }
        .strength-good { background: #28a745; width: 75%; }
        .strength-strong { background: #007bff; width: 100%; }
        
        .password-requirements {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
            text-align: left;
        }
        
        .requirement {
            margin-bottom: 5px;
        }
        
        .requirement.met {
            color: #28a745;
        }
        
        .btn-reset {
            background: linear-gradient(135deg, #2d5a27, #4a7c59);
            color: white;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
            min-height: 44px;
            margin-bottom: 25px;
        }
        
        .btn-reset:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(45, 90, 39, 0.3);
        }
        
        .btn-reset:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .back-link {
            color: #2d5a27;
            text-decoration: none;
            font-weight: 600;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .success-container {
            text-align: center;
        }
        
        .success-icon {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .reset-container {
                padding: 40px 20px;
            }
            
            .reset-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <?php if ($message): ?>
            <div class="success-container">
                <div class="success-icon">✅</div>
                <h1 class="reset-title">Password Reset Complete!</h1>
                <div class="success-message"><?php echo htmlspecialchars($message); ?></div>
                <p style="margin-bottom: 25px;">You can now log in with your new password.</p>
                <a href="member_login.php" class="btn-reset" style="display: inline-block; text-decoration: none;">
                    Go to Login
                </a>
            </div>
        <?php else: ?>
            <div class="reset-icon">🔑</div>
            <h1 class="reset-title">Reset Your Password</h1>
            <p class="reset-subtitle">
                Enter your new password below. Make sure it's secure and easy for you to remember.
            </p>
            
            <?php if ($error): ?>
                <div class="error-message">❌ <?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>
            
            <form method="POST" id="resetForm">
                <div class="form-group">
                    <label for="new_password">New Password</label>
                    <div class="password-container">
                        <input type="password" id="new_password" name="new_password" class="form-control" 
                               placeholder="Enter new password" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('new_password')">👁️</button>
                    </div>
                    <div class="password-strength">
                        <div class="password-strength-bar" id="strengthBar"></div>
                    </div>
                    <div class="password-requirements" id="requirements">
                        <?php 
                        $reqs = AuthConfig::getPasswordRequirements();
                        echo "<div class='requirement' id='req-length'>• At least {$reqs['min_length']} characters</div>";
                        if ($reqs['require_uppercase']) echo "<div class='requirement' id='req-upper'>• One uppercase letter</div>";
                        if ($reqs['require_lowercase']) echo "<div class='requirement' id='req-lower'>• One lowercase letter</div>";
                        if ($reqs['require_numbers']) echo "<div class='requirement' id='req-number'>• One number</div>";
                        if ($reqs['require_special']) echo "<div class='requirement' id='req-special'>• One special character</div>";
                        ?>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="confirm_password">Confirm New Password</label>
                    <div class="password-container">
                        <input type="password" id="confirm_password" name="confirm_password" class="form-control" 
                               placeholder="Confirm new password" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('confirm_password')">👁️</button>
                    </div>
                </div>
                
                <button type="submit" class="btn-reset" id="resetBtn">Reset Password</button>
            </form>
            
            <a href="member_login.php" class="back-link">← Back to Login</a>
        <?php endif; ?>
    </div>
    
    <script>
        function togglePassword(fieldId) {
            const passwordField = document.getElementById(fieldId);
            const toggleBtn = passwordField.nextElementSibling;
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleBtn.textContent = '🙈';
            } else {
                passwordField.type = 'password';
                toggleBtn.textContent = '👁️';
            }
        }
        
        // Password strength checker
        document.getElementById('new_password').addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.getElementById('strengthBar');
            const requirements = <?php echo json_encode(AuthConfig::getPasswordRequirements()); ?>;
            
            let score = 0;
            let metRequirements = 0;
            const totalRequirements = Object.values(requirements).filter(v => v === true).length + 1; // +1 for length
            
            // Check length
            const lengthReq = document.getElementById('req-length');
            if (password.length >= requirements.min_length) {
                lengthReq.classList.add('met');
                metRequirements++;
                score += 25;
            } else {
                lengthReq.classList.remove('met');
            }
            
            // Check uppercase
            const upperReq = document.getElementById('req-upper');
            if (upperReq) {
                if (/[A-Z]/.test(password)) {
                    upperReq.classList.add('met');
                    metRequirements++;
                    score += 20;
                } else {
                    upperReq.classList.remove('met');
                }
            }
            
            // Check lowercase
            const lowerReq = document.getElementById('req-lower');
            if (lowerReq) {
                if (/[a-z]/.test(password)) {
                    lowerReq.classList.add('met');
                    metRequirements++;
                    score += 20;
                } else {
                    lowerReq.classList.remove('met');
                }
            }
            
            // Check numbers
            const numberReq = document.getElementById('req-number');
            if (numberReq) {
                if (/\d/.test(password)) {
                    numberReq.classList.add('met');
                    metRequirements++;
                    score += 20;
                } else {
                    numberReq.classList.remove('met');
                }
            }
            
            // Check special characters
            const specialReq = document.getElementById('req-special');
            if (specialReq) {
                if (/[^a-zA-Z\d]/.test(password)) {
                    specialReq.classList.add('met');
                    metRequirements++;
                    score += 15;
                } else {
                    specialReq.classList.remove('met');
                }
            }
            
            // Update strength bar
            strengthBar.className = 'password-strength-bar';
            if (score >= 80) {
                strengthBar.classList.add('strength-strong');
            } else if (score >= 60) {
                strengthBar.classList.add('strength-good');
            } else if (score >= 40) {
                strengthBar.classList.add('strength-fair');
            } else if (score >= 20) {
                strengthBar.classList.add('strength-weak');
            }
        });
        
        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            const resetBtn = document.getElementById('resetBtn');
            
            if (confirmPassword && newPassword !== confirmPassword) {
                this.style.borderColor = '#dc3545';
                resetBtn.disabled = true;
            } else {
                this.style.borderColor = '#ddd';
                resetBtn.disabled = false;
            }
        });
        
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('new_password').focus();
        });
    </script>
</body>
</html>
