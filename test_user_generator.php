<?php
/**
 * ExtremeLife MLM Test User Generator
 * Creates test users for development and testing
 */

session_start();

// Only allow in testing mode
require_once 'auth_config.php';

if (!AuthConfig::isTestingMode()) {
    die('Test user generator is only available in testing mode');
}

require_once 'config/database.php';
require_once 'enhanced_auth.php';

$message = '';
$error = '';
$generated_users = [];

// Initialize database connection
$config = include 'config/database.php';
$pdo = new PDO("mysql:host={$config['host']};dbname={$config['dbname']}", $config['username'], $config['password']);
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

// Handle user generation
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'generate_users') {
        $count = intval($_POST['user_count'] ?? 5);
        $role = $_POST['role'] ?? 'member';
        
        $generated_users = generateTestUsers($pdo, $count, $role);
        $message = "Generated {$count} test users successfully!";
        
    } elseif ($action === 'create_standard_users') {
        $result = createStandardTestUsers($pdo);
        if ($result['success']) {
            $message = $result['message'];
            $generated_users = $result['users'];
        } else {
            $error = $result['message'];
        }
        
    } elseif ($action === 'cleanup_test_users') {
        $result = cleanupTestUsers($pdo);
        $message = $result['message'];
    }
}

function generateTestUsers($pdo, $count, $role) {
    $users = [];
    
    for ($i = 1; $i <= $count; $i++) {
        $username = "test{$role}{$i}";
        $email = "test{$role}{$i}@extremelife.test";
        $password = "test123";
        $first_name = "Test";
        $last_name = ucfirst($role) . $i;
        $referral_code = strtoupper(substr($role, 0, 3)) . str_pad($i, 3, '0', STR_PAD_LEFT);
        
        try {
            // Insert member
            $stmt = $pdo->prepare("
                INSERT INTO mlm_members 
                (first_name, last_name, email, referral_code, status, current_rank, joined_date, is_test_account) 
                VALUES (?, ?, ?, ?, 'active', ?, NOW(), 1)
                ON DUPLICATE KEY UPDATE email = VALUES(email)
            ");
            $stmt->execute([$first_name, $last_name, $email, $referral_code, ucfirst($role)]);
            
            $member_id = $pdo->lastInsertId();
            if (!$member_id) {
                // Get existing member ID
                $stmt = $pdo->prepare("SELECT id FROM mlm_members WHERE email = ?");
                $stmt->execute([$email]);
                $member_id = $stmt->fetchColumn();
            }
            
            // Insert credentials
            $password_hash = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("
                INSERT INTO mlm_user_credentials 
                (member_id, password_hash, created_at) 
                VALUES (?, ?, NOW())
                ON DUPLICATE KEY UPDATE password_hash = VALUES(password_hash)
            ");
            $stmt->execute([$member_id, $password_hash]);
            
            $users[] = [
                'id' => $member_id,
                'username' => $username,
                'email' => $email,
                'password' => $password,
                'name' => "$first_name $last_name",
                'role' => $role,
                'referral_code' => $referral_code
            ];
            
        } catch (Exception $e) {
            error_log("Error creating test user: " . $e->getMessage());
        }
    }
    
    return $users;
}

function createStandardTestUsers($pdo) {
    $standard_users = [
        [
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password' => 'admin123',
            'first_name' => 'Admin',
            'last_name' => 'User',
            'role' => 'admin',
            'referral_code' => 'ADMIN001'
        ],
        [
            'username' => 'manager',
            'email' => '<EMAIL>',
            'password' => 'manager123',
            'first_name' => 'Manager',
            'last_name' => 'User',
            'role' => 'manager',
            'referral_code' => 'MGR001'
        ],
        [
            'username' => 'testmember',
            'email' => '<EMAIL>',
            'password' => 'test123',
            'first_name' => 'Test',
            'last_name' => 'Member',
            'role' => 'member',
            'referral_code' => 'MEM001'
        ]
    ];
    
    $created_users = [];
    
    try {
        foreach ($standard_users as $user) {
            // Insert member
            $stmt = $pdo->prepare("
                INSERT INTO mlm_members 
                (first_name, last_name, email, referral_code, status, current_rank, joined_date, is_test_account) 
                VALUES (?, ?, ?, ?, 'active', ?, NOW(), 1)
                ON DUPLICATE KEY UPDATE 
                first_name = VALUES(first_name),
                last_name = VALUES(last_name),
                referral_code = VALUES(referral_code),
                current_rank = VALUES(current_rank)
            ");
            $stmt->execute([
                $user['first_name'], 
                $user['last_name'], 
                $user['email'], 
                $user['referral_code'], 
                ucfirst($user['role'])
            ]);
            
            $member_id = $pdo->lastInsertId();
            if (!$member_id) {
                // Get existing member ID
                $stmt = $pdo->prepare("SELECT id FROM mlm_members WHERE email = ?");
                $stmt->execute([$user['email']]);
                $member_id = $stmt->fetchColumn();
            }
            
            // Insert credentials
            $password_hash = password_hash($user['password'], PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("
                INSERT INTO mlm_user_credentials 
                (member_id, password_hash, created_at) 
                VALUES (?, ?, NOW())
                ON DUPLICATE KEY UPDATE password_hash = VALUES(password_hash)
            ");
            $stmt->execute([$member_id, $password_hash]);
            
            $created_users[] = array_merge($user, ['id' => $member_id]);
        }
        
        return [
            'success' => true,
            'message' => 'Standard test users created successfully!',
            'users' => $created_users
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Error creating standard test users: ' . $e->getMessage()
        ];
    }
}

function cleanupTestUsers($pdo) {
    try {
        // Get test user IDs
        $stmt = $pdo->prepare("SELECT id FROM mlm_members WHERE is_test_account = 1 OR email LIKE '%@extremelife.test'");
        $stmt->execute();
        $test_user_ids = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($test_user_ids)) {
            return ['message' => 'No test users found to cleanup'];
        }
        
        $placeholders = str_repeat('?,', count($test_user_ids) - 1) . '?';
        
        // Delete from related tables
        $tables = [
            'mlm_user_credentials',
            'mlm_auth_audit',
            'mlm_remember_tokens',
            'mlm_password_reset_tokens',
            'mlm_group_sales'
        ];
        
        foreach ($tables as $table) {
            $stmt = $pdo->prepare("DELETE FROM {$table} WHERE member_id IN ({$placeholders})");
            $stmt->execute($test_user_ids);
        }
        
        // Delete members
        $stmt = $pdo->prepare("DELETE FROM mlm_members WHERE id IN ({$placeholders})");
        $stmt->execute($test_user_ids);
        
        $count = count($test_user_ids);
        return ['message' => "Cleaned up {$count} test users and related data"];
        
    } catch (Exception $e) {
        return ['message' => 'Error during cleanup: ' . $e->getMessage()];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test User Generator - ExtremeLife MLM</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            background: linear-gradient(135deg, #2d5a27, #4a7c59);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .testing-banner {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 600;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .card-title {
            color: #2d5a27;
            font-size: 1.5rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-control {
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            min-height: 44px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #2d5a27;
        }
        
        .btn {
            background: #2d5a27;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s;
            min-height: 44px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn:hover {
            background: #4a7c59;
            transform: translateY(-1px);
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .users-table th,
        .users-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .users-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2d5a27;
        }
        
        .users-table tr:hover {
            background: #f8f9fa;
        }
        
        .credential-display {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .users-table {
                font-size: 12px;
            }
            
            .users-table th,
            .users-table td {
                padding: 8px 4px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 ExtremeLife MLM Test User Generator</h1>
            <p>Create and manage test users for development and testing</p>
        </div>
        
        <div class="testing-banner">
            🔧 TESTING MODE ACTIVE - This tool is only available in development mode
        </div>
        
        <?php if ($message): ?>
            <div class="message">✅ <?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="error">❌ <?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <!-- Standard Test Users -->
        <div class="card">
            <h2 class="card-title">🎯 Create Standard Test Users</h2>
            <p style="margin-bottom: 20px;">Create the standard set of test users (admin, manager, member) with predefined credentials.</p>
            
            <form method="POST">
                <input type="hidden" name="action" value="create_standard_users">
                <button type="submit" class="btn">Create Standard Users</button>
            </form>
        </div>
        
        <!-- Custom User Generation -->
        <div class="card">
            <h2 class="card-title">👥 Generate Custom Test Users</h2>
            
            <form method="POST">
                <input type="hidden" name="action" value="generate_users">
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="user_count">Number of Users</label>
                        <input type="number" id="user_count" name="user_count" class="form-control" 
                               value="5" min="1" max="50" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="role">User Role</label>
                        <select id="role" name="role" class="form-control">
                            <option value="member">Member</option>
                            <option value="manager">Manager</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn">Generate Users</button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Cleanup -->
        <div class="card">
            <h2 class="card-title">🧹 Cleanup Test Users</h2>
            <p style="margin-bottom: 20px;">Remove all test users and their associated data from the database.</p>
            
            <form method="POST" onsubmit="return confirm('Are you sure you want to delete all test users? This action cannot be undone.')">
                <input type="hidden" name="action" value="cleanup_test_users">
                <button type="submit" class="btn btn-danger">Cleanup All Test Users</button>
            </form>
        </div>
        
        <!-- Generated Users Display -->
        <?php if (!empty($generated_users)): ?>
            <div class="card">
                <h2 class="card-title">📋 Generated Users</h2>
                
                <table class="users-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Password</th>
                            <th>Role</th>
                            <th>Referral Code</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($generated_users as $user): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($user['id']); ?></td>
                                <td><?php echo htmlspecialchars($user['name']); ?></td>
                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                <td><span class="credential-display"><?php echo htmlspecialchars($user['password']); ?></span></td>
                                <td><?php echo htmlspecialchars($user['role']); ?></td>
                                <td><?php echo htmlspecialchars($user['referral_code']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="member_login.php" class="btn btn-secondary">← Back to Login</a>
        </div>
    </div>
</body>
</html>
