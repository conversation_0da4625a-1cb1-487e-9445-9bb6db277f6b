<?php
/**
 * ExtremeLife MLM Password Reset Verification Tool
 * Verifies that all passwords have been updated to "test123"
 */

require_once 'config/database.php';
require_once 'auth_config.php';
require_once 'enhanced_auth.php';

// Only allow in testing mode
if (!AuthConfig::isTestingMode()) {
    die('This verification tool is only available in testing mode.');
}

// Initialize database connection
$config = include 'config/database.php';
$pdo = new PDO("mysql:host={$config['host']};dbname={$config['dbname']}", $config['username'], $config['password']);
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

$test_password = 'test123';
$verification_results = [];

try {
    // Get all members with credentials
    $stmt = $pdo->query("
        SELECT m.id, m.first_name, m.last_name, m.email, m.current_rank, 
               uc.password_hash, uc.failed_attempts, uc.locked_until
        FROM mlm_members m
        JOIN mlm_user_credentials uc ON m.id = uc.member_id
        ORDER BY m.id
    ");
    
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $total_members = count($members);
    $verified_count = 0;
    $failed_verifications = [];
    
    foreach ($members as $member) {
        $can_login = password_verify($test_password, $member['password_hash']);
        $is_locked = $member['locked_until'] && strtotime($member['locked_until']) > time();
        $has_failed_attempts = $member['failed_attempts'] > 0;
        
        if ($can_login && !$is_locked && !$has_failed_attempts) {
            $verified_count++;
        } else {
            $failed_verifications[] = [
                'member' => $member,
                'can_login' => $can_login,
                'is_locked' => $is_locked,
                'has_failed_attempts' => $has_failed_attempts
            ];
        }
    }
    
    $verification_results = [
        'success' => $verified_count === $total_members,
        'total_members' => $total_members,
        'verified_count' => $verified_count,
        'failed_count' => count($failed_verifications),
        'failed_verifications' => $failed_verifications
    ];
    
} catch (Exception $e) {
    $verification_results = [
        'success' => false,
        'error' => $e->getMessage()
    ];
}

// Test login functionality with enhanced auth
$auth = new EnhancedAuth($pdo);
$login_tests = [];

// Test with standard test credentials
$test_credentials = AuthConfig::getTestCredentials();
foreach ($test_credentials as $role => $creds) {
    $result = $auth->authenticate($creds['username'], $creds['password']);
    $login_tests[] = [
        'role' => $role,
        'username' => $creds['username'],
        'password' => $creds['password'],
        'success' => $result['success'],
        'message' => $result['message'] ?? 'Login successful'
    ];
}

// Test with actual member accounts (first 3)
if (!empty($members)) {
    $sample_members = array_slice($members, 0, 3);
    foreach ($sample_members as $member) {
        $result = $auth->authenticate($member['email'], $test_password);
        $login_tests[] = [
            'role' => 'member',
            'username' => $member['email'],
            'password' => $test_password,
            'success' => $result['success'],
            'message' => $result['message'] ?? 'Login successful',
            'member_name' => $member['first_name'] . ' ' . $member['last_name']
        ];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Verification - ExtremeLife MLM</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            background: linear-gradient(135deg, #2d5a27, #4a7c59);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .card-title {
            color: #2d5a27;
            font-size: 1.5rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #2d5a27;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #2d5a27;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .verification-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .verification-table th,
        .verification-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .verification-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2d5a27;
        }
        
        .verification-table tr:hover {
            background: #f8f9fa;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-success-badge {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error-badge {
            background: #f8d7da;
            color: #721c24;
        }
        
        .btn {
            background: #2d5a27;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s;
            min-height: 44px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-right: 10px;
        }
        
        .btn:hover {
            background: #4a7c59;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .verification-table {
                font-size: 12px;
            }
            
            .verification-table th,
            .verification-table td {
                padding: 8px 4px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 ExtremeLife MLM Password Reset Verification</h1>
            <p>Verify that all member passwords have been updated to "test123"</p>
        </div>
        
        <!-- Overall Status -->
        <?php if (isset($verification_results['error'])): ?>
            <div class="status-error">
                ❌ Verification Error: <?php echo htmlspecialchars($verification_results['error']); ?>
            </div>
        <?php elseif ($verification_results['success']): ?>
            <div class="status-success">
                ✅ All member passwords have been successfully updated to "test123"!
            </div>
        <?php else: ?>
            <div class="status-error">
                ❌ Password verification failed for some members. See details below.
            </div>
        <?php endif; ?>
        
        <!-- Statistics -->
        <?php if (!isset($verification_results['error'])): ?>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $verification_results['total_members']; ?></div>
                    <div class="stat-label">Total Members</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $verification_results['verified_count']; ?></div>
                    <div class="stat-label">Verified Passwords</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $verification_results['failed_count']; ?></div>
                    <div class="stat-label">Failed Verifications</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $verification_results['total_members'] > 0 ? round(($verification_results['verified_count'] / $verification_results['total_members']) * 100, 1) : 0; ?>%</div>
                    <div class="stat-label">Success Rate</div>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Failed Verifications -->
        <?php if (!empty($verification_results['failed_verifications'])): ?>
            <div class="card">
                <h2 class="card-title">❌ Failed Verifications</h2>
                
                <table class="verification-table">
                    <thead>
                        <tr>
                            <th>Member</th>
                            <th>Email</th>
                            <th>Can Login</th>
                            <th>Account Locked</th>
                            <th>Failed Attempts</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($verification_results['failed_verifications'] as $failed): ?>
                            <tr>
                                <td>
                                    <?php echo htmlspecialchars($failed['member']['first_name'] . ' ' . $failed['member']['last_name']); ?>
                                    <br><small>ID: <?php echo $failed['member']['id']; ?></small>
                                </td>
                                <td><?php echo htmlspecialchars($failed['member']['email']); ?></td>
                                <td>
                                    <span class="status-badge <?php echo $failed['can_login'] ? 'status-success-badge' : 'status-error-badge'; ?>">
                                        <?php echo $failed['can_login'] ? 'Yes' : 'No'; ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="status-badge <?php echo $failed['is_locked'] ? 'status-error-badge' : 'status-success-badge'; ?>">
                                        <?php echo $failed['is_locked'] ? 'Yes' : 'No'; ?>
                                    </span>
                                </td>
                                <td><?php echo $failed['member']['failed_attempts']; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
        
        <!-- Login Tests -->
        <div class="card">
            <h2 class="card-title">🧪 Login Functionality Tests</h2>
            
            <table class="verification-table">
                <thead>
                    <tr>
                        <th>Account Type</th>
                        <th>Username</th>
                        <th>Password</th>
                        <th>Login Status</th>
                        <th>Message</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($login_tests as $test): ?>
                        <tr>
                            <td>
                                <?php echo ucfirst($test['role']); ?>
                                <?php if (isset($test['member_name'])): ?>
                                    <br><small><?php echo htmlspecialchars($test['member_name']); ?></small>
                                <?php endif; ?>
                            </td>
                            <td><?php echo htmlspecialchars($test['username']); ?></td>
                            <td><code><?php echo htmlspecialchars($test['password']); ?></code></td>
                            <td>
                                <span class="status-badge <?php echo $test['success'] ? 'status-success-badge' : 'status-error-badge'; ?>">
                                    <?php echo $test['success'] ? 'Success' : 'Failed'; ?>
                                </span>
                            </td>
                            <td><?php echo htmlspecialchars($test['message']); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="admin_mass_password_reset.php" class="btn">← Back to Password Reset Tool</a>
            <a href="member_login.php" class="btn btn-secondary">Test Login</a>
            <button onclick="location.reload()" class="btn btn-secondary">🔄 Refresh Verification</button>
        </div>
    </div>
</body>
</html>
