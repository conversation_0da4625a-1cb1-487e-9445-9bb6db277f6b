<?php
/**
 * ExtremeLife MLM Web-based Database Schema Update
 * Adds missing columns for enhanced authentication system
 */

require_once 'config/database.php';

$message = '';
$error = '';
$updates_applied = [];
$errors = [];
$current_structure = [];

// Initialize database connection
$config = include 'config/database.php';
$pdo = new PDO("mysql:host={$config['host']};dbname={$config['dbname']}", $config['username'], $config['password']);
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

// Get current table structure
try {
    $stmt = $pdo->query("DESCRIBE mlm_user_credentials");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $current_structure[] = $row;
    }
} catch (Exception $e) {
    $error = "Failed to read current table structure: " . $e->getMessage();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    if ($action === 'update_schema') {
        try {
            // Check current columns
            $stmt = $pdo->query("DESCRIBE mlm_user_credentials");
            $existing_columns = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $existing_columns[] = $row['Field'];
            }
            
            // Define required columns
            $required_columns = [
                'failed_attempts' => 'INT DEFAULT 0',
                'locked_until' => 'TIMESTAMP NULL',
                'last_login' => 'TIMESTAMP NULL',
                'two_factor_enabled' => 'BOOLEAN DEFAULT FALSE',
                'two_factor_secret' => 'VARCHAR(255) NULL',
                'password_reset_at' => 'TIMESTAMP NULL'
            ];
            
            // Add missing columns
            foreach ($required_columns as $column => $definition) {
                if (!in_array($column, $existing_columns)) {
                    try {
                        $sql = "ALTER TABLE mlm_user_credentials ADD COLUMN {$column} {$definition}";
                        $pdo->exec($sql);
                        $updates_applied[] = "Added column: {$column}";
                    } catch (Exception $e) {
                        $errors[] = "Failed to add column {$column}: " . $e->getMessage();
                    }
                }
            }
            
            // Create authentication audit table
            try {
                $pdo->exec("
                    CREATE TABLE IF NOT EXISTS mlm_auth_audit (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        member_id INT NULL,
                        username VARCHAR(100),
                        action VARCHAR(50),
                        ip_address VARCHAR(45),
                        user_agent TEXT,
                        success BOOLEAN,
                        failure_reason VARCHAR(255),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        INDEX idx_member_id (member_id),
                        INDEX idx_created_at (created_at)
                    )
                ");
                $updates_applied[] = "Created/verified mlm_auth_audit table";
            } catch (Exception $e) {
                $errors[] = "Failed to create mlm_auth_audit table: " . $e->getMessage();
            }
            
            // Create remember me tokens table
            try {
                $pdo->exec("
                    CREATE TABLE IF NOT EXISTS mlm_remember_tokens (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        member_id INT NOT NULL,
                        token_hash VARCHAR(255) NOT NULL,
                        expires_at TIMESTAMP NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        INDEX idx_member_id (member_id),
                        INDEX idx_token_hash (token_hash),
                        INDEX idx_expires_at (expires_at)
                    )
                ");
                $updates_applied[] = "Created/verified mlm_remember_tokens table";
            } catch (Exception $e) {
                $errors[] = "Failed to create mlm_remember_tokens table: " . $e->getMessage();
            }
            
            // Create password reset tokens table
            try {
                $pdo->exec("
                    CREATE TABLE IF NOT EXISTS mlm_password_reset_tokens (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        member_id INT NOT NULL,
                        token VARCHAR(255) NOT NULL,
                        expires_at TIMESTAMP NOT NULL,
                        used BOOLEAN DEFAULT FALSE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        INDEX idx_member_id (member_id),
                        INDEX idx_token (token)
                    )
                ");
                $updates_applied[] = "Created/verified mlm_password_reset_tokens table";
            } catch (Exception $e) {
                $errors[] = "Failed to create mlm_password_reset_tokens table: " . $e->getMessage();
            }
            
            // Add is_test_account column to mlm_members
            try {
                $stmt = $pdo->query("DESCRIBE mlm_members");
                $member_columns = [];
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    $member_columns[] = $row['Field'];
                }
                
                if (!in_array('is_test_account', $member_columns)) {
                    $pdo->exec("ALTER TABLE mlm_members ADD COLUMN is_test_account BOOLEAN DEFAULT FALSE");
                    $updates_applied[] = "Added is_test_account column to mlm_members";
                }
            } catch (Exception $e) {
                $errors[] = "Failed to add is_test_account column: " . $e->getMessage();
            }
            
            // Update current structure after changes
            $current_structure = [];
            $stmt = $pdo->query("DESCRIBE mlm_user_credentials");
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $current_structure[] = $row;
            }
            
            if (empty($errors)) {
                $message = "Database schema updated successfully! " . count($updates_applied) . " changes applied.";
            } else {
                $error = "Schema update completed with " . count($errors) . " errors. See details below.";
            }
            
        } catch (Exception $e) {
            $error = "Critical error during schema update: " . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Schema Update - ExtremeLife MLM</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            background: linear-gradient(135deg, #2d5a27, #4a7c59);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .card-title {
            color: #2d5a27;
            font-size: 1.5rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .btn {
            background: #2d5a27;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s;
            min-height: 44px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn:hover {
            background: #4a7c59;
            transform: translateY(-1px);
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        
        .schema-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }
        
        .schema-table th,
        .schema-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .schema-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2d5a27;
        }
        
        .schema-table tr:hover {
            background: #f8f9fa;
        }
        
        .update-list {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .update-item {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
        }
        
        .update-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .update-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .card {
                padding: 20px;
            }
            
            .schema-table {
                font-size: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 ExtremeLife MLM Database Schema Update</h1>
            <p>Add missing columns for enhanced authentication system</p>
        </div>
        
        <div class="warning-box">
            ⚠️ <strong>Important:</strong> This will modify your database structure. Make sure you have a backup before proceeding.
        </div>
        
        <?php if ($message): ?>
            <div class="message">✅ <?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="error">❌ <?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <!-- Current Table Structure -->
        <div class="card">
            <h2 class="card-title">📋 Current mlm_user_credentials Table Structure</h2>
            
            <?php if (!empty($current_structure)): ?>
                <div class="table-container">
                    <table class="schema-table">
                        <thead>
                            <tr>
                                <th>Field</th>
                                <th>Type</th>
                                <th>Null</th>
                                <th>Key</th>
                                <th>Default</th>
                                <th>Extra</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($current_structure as $column): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($column['Field']); ?></strong></td>
                                    <td><?php echo htmlspecialchars($column['Type']); ?></td>
                                    <td><?php echo htmlspecialchars($column['Null']); ?></td>
                                    <td><?php echo htmlspecialchars($column['Key']); ?></td>
                                    <td><?php echo htmlspecialchars($column['Default'] ?? 'NULL'); ?></td>
                                    <td><?php echo htmlspecialchars($column['Extra']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <p>Unable to read current table structure.</p>
            <?php endif; ?>
        </div>
        
        <!-- Schema Update Action -->
        <div class="card">
            <h2 class="card-title">🔄 Update Database Schema</h2>
            <p style="margin-bottom: 20px;">
                This will add the following missing columns and tables required for the enhanced authentication system:
            </p>
            
            <ul style="margin-bottom: 20px; padding-left: 20px;">
                <li><strong>mlm_user_credentials</strong>: failed_attempts, locked_until, last_login, two_factor_enabled, two_factor_secret, password_reset_at</li>
                <li><strong>mlm_auth_audit</strong>: Authentication audit logging table</li>
                <li><strong>mlm_remember_tokens</strong>: Remember me functionality table</li>
                <li><strong>mlm_password_reset_tokens</strong>: Password reset tokens table</li>
                <li><strong>mlm_members</strong>: is_test_account column</li>
            </ul>
            
            <form method="POST" onsubmit="return confirm('Are you sure you want to update the database schema? Make sure you have a backup!')">
                <input type="hidden" name="action" value="update_schema">
                <button type="submit" class="btn">🔧 Update Database Schema</button>
            </form>
        </div>
        
        <!-- Update Results -->
        <?php if (!empty($updates_applied) || !empty($errors)): ?>
            <div class="card">
                <h2 class="card-title">📊 Update Results</h2>
                
                <div class="update-list">
                    <?php if (!empty($updates_applied)): ?>
                        <h4 style="color: #155724; margin-bottom: 15px;">✅ Successful Updates:</h4>
                        <?php foreach ($updates_applied as $update): ?>
                            <div class="update-item update-success">
                                ✅ <?php echo htmlspecialchars($update); ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                    
                    <?php if (!empty($errors)): ?>
                        <h4 style="color: #721c24; margin-bottom: 15px; margin-top: 20px;">❌ Errors:</h4>
                        <?php foreach ($errors as $error_item): ?>
                            <div class="update-item update-error">
                                ❌ <?php echo htmlspecialchars($error_item); ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="admin_mass_password_reset.php" class="btn">← Back to Password Reset Tool</a>
        </div>
    </div>
</body>
</html>
